version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: meka1_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./src:/var/www/html
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - php
    networks:
      - meka1_network

  php:
    image: php:8.2-fpm-alpine
    container_name: meka1_php
    volumes:
      - ./src:/var/www/html
    networks:
      - meka1_network
    expose:
      - "9000"

networks:
  meka1_network:
    driver: bridge