# Asya Fresh - Website Clone

A responsive website clone of asyafresh.com built with Docker, PHP, nginx, and Bootstrap.

## Features

- **Docker Environment**: PHP 8.2 + nginx setup
- **Responsive Design**: Bootstrap 5 with mobile-first approach
- **Modern UI**: Clean, professional design matching the original
- **No Database**: Static content implementation
- **SEO Friendly**: Semantic HTML structure
- **Performance Optimized**: Compressed assets and lazy loading

## Tech Stack

- **Backend**: PHP 8.2-FPM
- **Web Server**: nginx (Alpine)
- **Frontend**: Bootstrap 5.3.0
- **Icons**: Font Awesome 6.4.0
- **Containerization**: Docker & Docker Compose

## Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd meka05
   ```

2. **Start the application**
   ```bash
   docker-compose up -d
   ```

3. **Access the website**
   - Open http://localhost in your browser

## Project Structure

```
.
├── docker-compose.yml          # Docker services configuration
├── nginx/                      # nginx configuration
│   ├── nginx.conf             # Main nginx config
│   └── default.conf           # Site-specific config
└── src/                       # Website source code
    ├── index.php              # Main page
    └── assets/
        ├── css/
        │   └── style.css      # Custom styles
        ├── js/
        │   └── script.js      # Interactive functionality
        └── images/            # Product and content images
```

## Features Implemented

### Navigation
- Fixed top navigation bar
- Smooth scrolling to sections
- Mobile-responsive hamburger menu
- Active section highlighting

### Sections
- **Hero**: Eye-catching banner with call-to-action
- **About**: Company overview with service highlights
- **Products**: Pomegranate, Cherry, Nectarine/Peach showcase
- **Logistics**: Global shipping capabilities
- **News**: Latest company updates
- **Contact**: Contact form and information

### Interactive Elements
- WhatsApp floating button
- Contact form with validation
- Hover effects and animations
- Responsive image galleries

### Mobile Optimization
- Bootstrap responsive grid
- Mobile-first CSS approach
- Touch-friendly navigation
- Optimized typography scaling

## Docker Services

### nginx
- **Image**: nginx:alpine
- **Ports**: 80:80, 443:443
- **Purpose**: Web server and reverse proxy

### PHP
- **Image**: php:8.2-fpm-alpine
- **Purpose**: PHP processing
- **Features**: FPM for performance

## Development

### Adding Images
Place product images in `src/assets/images/`:
- `hero-image.jpg` - Main hero section image
- `pomegranate.jpg` - Pomegranate product image
- `cherry.jpg` - Cherry product image
- `peach.jpg` - Peach/Nectarine product image
- `logistics.jpg` - Logistics section image

### Customization
- **Colors**: Modify CSS variables in `style.css`
- **Content**: Update text in `index.php`
- **Styling**: Add custom CSS in `style.css`
- **Functionality**: Extend JavaScript in `script.js`

## Production Deployment

For production deployment:

1. **Update docker-compose.yml**
   - Add SSL certificates
   - Configure proper domain names
   - Set up environment variables

2. **Optimize assets**
   - Minify CSS and JavaScript
   - Compress images
   - Enable gzip compression

3. **Security**
   - Update nginx security headers
   - Configure firewall rules
   - Regular security updates

## Commands

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# Rebuild after changes
docker-compose up --build

# Shell access
docker-compose exec php sh
docker-compose exec nginx sh
```

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## License

This project is created for educational purposes as a clone of asyafresh.com.