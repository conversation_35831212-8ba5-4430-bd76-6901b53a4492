:root {
    --primary-color: #2c5530;
    --secondary-color: #4a7c59;
    --accent-color: #8fbc8f;
    --text-dark: #2c3e50;
    --text-light: #6c757d;
    --bg-light: #f8f9fa;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

/* Bootstrap overrides */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    font-weight: 500;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Navigation */
.navbar {
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    color: var(--primary-color) !important;
    letter-spacing: 1px;
}

.nav-link {
    font-weight: 500;
    color: var(--text-dark) !important;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color) !important;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Hero Section */
.hero-section {
    margin-top: 76px; /* Account for fixed navbar */
    position: relative;
}

.hero-image {
    height: 70vh;
    min-height: 400px;
    max-height: 600px;
    object-fit: cover;
    object-position: center;
}

/* Cards */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* Sections */
section {
    scroll-margin-top: 80px;
}

.section-title {
    position: relative;
    display: inline-block;
    margin-bottom: 3rem;
}

.section-title::after {
    content: '';
    position: absolute;
    width: 50px;
    height: 3px;
    background: var(--primary-color);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

/* Products Section */
#products .card-img-top {
    height: 250px;
}

/* Product Cards */
.product-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.product-image-container {
    padding: 20px 20px 0 20px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: contain;
    border-radius: 15px;
}

.product-content {
    padding: 20px 30px 30px 30px;
    text-align: center;
}

.product-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.product-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 20px;
    line-height: 1.4;
}

.btn-review {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 10px 30px;
    border-radius: 25px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-review:hover {
    background-color: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    color: white;
}

/* Contact Form */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    width: 60px;
    height: 60px;
    bottom: 30px;
    right: 30px;
    background-color: #25d366;
    color: white;
    border-radius: 50px;
    text-align: center;
    font-size: 30px;
    line-height: 60px;
    box-shadow: 2px 2px 3px #999;
    z-index: 1000;
    transition: all 0.3s ease;
    text-decoration: none;
}

.whatsapp-float:hover {
    color: white;
    background-color: #128c7e;
    transform: scale(1.1);
}

/* Footer */
footer {
    background: linear-gradient(45deg, #2c3e50 0%, #34495e 100%);
}

footer a {
    transition: color 0.3s ease;
}

footer a:hover {
    color: var(--accent-color) !important;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        margin-top: 70px;
    }

    .hero-image {
        height: 50vh;
        min-height: 300px;
    }

    .display-5 {
        font-size: 2rem;
    }

    .whatsapp-float {
        width: 50px;
        height: 50px;
        line-height: 50px;
        font-size: 24px;
        bottom: 20px;
        right: 20px;
    }

    .product-image-container {
        padding: 15px 15px 0 15px;
    }

    .product-image {
        height: 180px;
    }

    .product-content {
        padding: 15px 20px 25px 20px;
    }

    .product-title {
        font-size: 1.1rem;
    }

    .btn-review {
        padding: 8px 25px;
        font-size: 0.85rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        margin-top: 65px;
    }

    .hero-image {
        height: 40vh;
        min-height: 250px;
    }

    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading animation */
.loading {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Utility classes */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.border-radius-lg {
    border-radius: 20px !important;
}

.shadow-custom {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Icon styles */
.fa-3x {
    color: var(--primary-color);
}

/* Custom spacing */
.section-padding {
    padding: 80px 0;
}

.section-padding-sm {
    padding: 60px 0;
}

/* Hover effects */
.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-shadow {
    transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
    box-shadow: var(--shadow-lg);
}